{"name": "ai-doc", "version": "1.0.0", "description": "智能文档协作平台 - 集成实时协作、AI 辅助写作和丰富编辑功能", "private": true, "workspaces": ["frontend", "backend", "ai-service"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:ai\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:ai": "cd ai-service && python -m uvicorn src.main:app --reload --port 8000", "dev:ws": "cd backend && npm run dev:ws", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend && npm run test:ai", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:ai": "cd ai-service && pytest", "test:coverage": "npm run test:frontend -- --coverage && npm run test:backend -- --coverage && npm run test:ai -- --cov=src", "test:e2e": "cd frontend && npm run test:e2e", "lint": "npm run lint:frontend && npm run lint:backend && npm run lint:ai", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:ai": "cd ai-service && flake8 src tests", "format": "npm run format:frontend && npm run format:backend && npm run format:ai", "format:frontend": "cd frontend && npm run format", "format:backend": "cd backend && npm run format", "format:ai": "cd ai-service && black src tests", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "db:test": "cd backend && npm run db:test", "install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:ai", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:ai": "cd ai-service && pip install -r requirements.txt", "clean": "npm run clean:frontend && npm run clean:backend && npm run clean:ai", "clean:frontend": "cd frontend && rm -rf node_modules dist", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:ai": "cd ai-service && rm -rf __pycache__ .pytest_cache", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"frontend/**/*.{js,jsx,ts,tsx}": ["cd frontend && npm run lint:fix", "cd frontend && npm run format"], "backend/**/*.{js,ts}": ["cd backend && npm run lint:fix", "cd backend && npm run format"], "ai-service/**/*.py": ["cd ai-service && black", "cd ai-service && flake8"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ai-doc.git"}, "keywords": ["collaborative-editing", "rich-text-editor", "ai-writing-assistant", "real-time-collaboration", "document-management", "tiptap", "yjs", "react", "nodejs", "<PERSON><PERSON><PERSON>"], "author": "AI-Doc Team", "license": "MIT"}