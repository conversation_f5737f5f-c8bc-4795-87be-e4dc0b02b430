import React, { Suspense, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, CircularProgress } from '@mui/material'
import { useSelector } from 'react-redux'

import { RootState } from '@store/index'
import { useAuth } from '@hooks/useAuth'
import ProtectedRoute from '@components/common/ProtectedRoute'
import LoadingSpinner from '@components/common/LoadingSpinner'

// 懒加载页面组件
const LoginPage = React.lazy(() => import('@pages/auth/LoginPage'))
const RegisterPage = React.lazy(() => import('@pages/auth/RegisterPage'))
const DashboardPage = React.lazy(() => import('@pages/dashboard/DashboardPage'))
const DocumentEditPage = React.lazy(() => import('@pages/document/DocumentEditPage'))
const DocumentListPage = React.lazy(() => import('@pages/document/DocumentListPage'))
const SettingsPage = React.lazy(() => import('@pages/settings/SettingsPage'))
const NotFoundPage = React.lazy(() => import('@pages/error/NotFoundPage'))

// 页面加载组件
const PageLoader: React.FC = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    minHeight="100vh"
  >
    <CircularProgress size={40} />
  </Box>
)

const App: React.FC = () => {
  const { initializeAuth } = useAuth()
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth)

  // 初始化认证状态
  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])

  // 显示加载状态
  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'background.default' }}>
      <Suspense fallback={<PageLoader />}>
        <Routes>
          {/* 公开路由 */}
          <Route
            path="/login"
            element={
              isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
            }
          />
          <Route
            path="/register"
            element={
              isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />
            }
          />

          {/* 受保护的路由 */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <DashboardPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/documents"
            element={
              <ProtectedRoute>
                <DocumentListPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/document/:id"
            element={
              <ProtectedRoute>
                <DocumentEditPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/settings"
            element={
              <ProtectedRoute>
                <SettingsPage />
              </ProtectedRoute>
            }
          />

          {/* 默认重定向 */}
          <Route
            path="/"
            element={
              <Navigate
                to={isAuthenticated ? "/dashboard" : "/login"}
                replace
              />
            }
          />

          {/* 404 页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </Box>
  )
}

export default App
