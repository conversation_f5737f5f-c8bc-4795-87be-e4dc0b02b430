// 用户相关类型
export interface User {
  id: string
  email: string
  username: string
  displayName: string
  avatarUrl?: string
  status: 'active' | 'inactive' | 'suspended'
  preferences: UserPreferences
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  fontSize: number
  autoSave: boolean
  notifications: NotificationSettings
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  mentions: boolean
  comments: boolean
  collaborations: boolean
}

// 认证相关类型
export interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
  token: string | null
  error: string | null
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  username: string
  displayName: string
  password: string
  confirmPassword: string
}

// 文档相关类型
export interface Document {
  id: string
  title: string
  slug?: string
  ownerId: string
  folderId?: string
  templateId?: string
  content: any // TipTap JSON 格式
  metadata: DocumentMetadata
  tags: string[]
  status: 'draft' | 'published' | 'archived' | 'deleted'
  visibility: 'private' | 'shared' | 'public'
  version: number
  wordCount: number
  characterCount: number
  readingTimeMinutes: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
}

export interface DocumentMetadata {
  description?: string
  keywords?: string[]
  author?: string
  lastEditedBy?: string
  customFields?: Record<string, any>
}

export interface DocumentVersion {
  id: string
  documentId: string
  versionNumber: number
  content: any
  contentDiff?: any
  title: string
  changedBy: string
  changeSummary?: string
  changeType: 'manual_save' | 'auto_save' | 'collaboration' | 'ai_suggestion'
  wordCount: number
  characterCount: number
  createdAt: string
}

// 文件夹相关类型
export interface Folder {
  id: string
  name: string
  parentId?: string
  ownerId: string
  color?: string
  icon?: string
  position: number
  createdAt: string
  updatedAt: string
}

// 协作相关类型
export interface Collaborator {
  id: string
  documentId: string
  userId: string
  user: User
  role: 'owner' | 'editor' | 'commenter' | 'viewer'
  permissions: CollaboratorPermissions
  invitedBy?: string
  invitedAt: string
  acceptedAt?: string
}

export interface CollaboratorPermissions {
  canEdit: boolean
  canComment: boolean
  canShare: boolean
  canDelete: boolean
  canManagePermissions: boolean
}

export interface CollaborationSession {
  id: string
  documentId: string
  userId: string
  user: User
  sessionToken: string
  cursorPosition?: any
  selectionRange?: any
  isActive: boolean
  lastActivityAt: string
  createdAt: string
}

// 评论相关类型
export interface Comment {
  id: string
  documentId: string
  authorId: string
  author: User
  parentId?: string
  content: string
  contentJson?: any
  anchorPosition?: any
  selectionRange?: any
  status: 'active' | 'resolved' | 'deleted'
  resolvedBy?: string
  resolvedAt?: string
  createdAt: string
  updatedAt: string
  replies?: Comment[]
}

// 任务相关类型
export interface Task {
  id: string
  documentId: string
  createdBy: string
  assignedTo?: string
  title: string
  description?: string
  anchorPosition?: any
  status: 'todo' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  dueDate?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
}

// AI 相关类型
export interface AISession {
  id: string
  documentId: string
  userId: string
  sessionType: 'chat' | 'rewrite' | 'summarize' | 'proofread'
  context?: any
  status: 'active' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
}

export interface AIMessage {
  id: string
  sessionId: string
  role: 'user' | 'assistant' | 'system'
  content: string
  contentJson?: any
  metadata?: any
  createdAt: string
}

export interface AISuggestion {
  id: string
  documentId: string
  userId: string
  sessionId?: string
  suggestionType: 'rewrite' | 'expand' | 'summarize' | 'proofread'
  originalContent: any
  suggestedContent: any
  positionRange: any
  status: 'pending' | 'accepted' | 'rejected' | 'modified'
  confidenceScore?: number
  userFeedback?: string
  feedbackRating?: number
  createdAt: string
  appliedAt?: string
}

// 模板相关类型
export interface Template {
  id: string
  name: string
  description?: string
  category?: string
  content: any
  thumbnailUrl?: string
  isPublic: boolean
  createdBy: string
  usageCount: number
  createdAt: string
  updatedAt: string
}

// 导入导出相关类型
export interface ImportExportJob {
  id: string
  userId: string
  documentId?: string
  jobType: 'import' | 'export'
  fileFormat: 'docx' | 'pdf' | 'md' | 'txt' | 'html'
  originalFilename?: string
  fileSize?: number
  fileUrl?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progressPercentage: number
  errorMessage?: string
  resultData?: any
  createdAt: string
  completedAt?: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  timestamp: string
  requestId: string
}

export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: Array<{
      field: string
      message: string
    }>
  }
  timestamp: string
  requestId: string
}

// 分页相关类型
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 编辑器相关类型
export interface EditorState {
  content: any
  selection?: any
  isEditable: boolean
  isFocused: boolean
  wordCount: number
  characterCount: number
}

export interface EditorCommand {
  name: string
  params?: any
}

// 实时协作相关类型
export interface CollaborationEvent {
  type: 'user-joined' | 'user-left' | 'cursor-moved' | 'selection-changed' | 'content-changed'
  userId: string
  data?: any
  timestamp: string
}

// 通知相关类型
export interface Notification {
  id: string
  userId: string
  type: 'mention' | 'comment' | 'collaboration' | 'system'
  title: string
  message: string
  data?: any
  isRead: boolean
  createdAt: string
}

// 搜索相关类型
export interface SearchResult {
  id: string
  type: 'document' | 'folder' | 'user'
  title: string
  description?: string
  url: string
  relevanceScore: number
  highlights?: string[]
}

export interface SearchFilters {
  type?: string[]
  dateRange?: {
    start: string
    end: string
  }
  author?: string[]
  tags?: string[]
  status?: string[]
}
