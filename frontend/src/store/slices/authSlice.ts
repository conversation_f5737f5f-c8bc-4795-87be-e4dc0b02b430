import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AuthState, User, LoginCredentials, RegisterData } from '@types/index'
import { authAPI } from '@api/auth'
import { tokenStorage } from '@utils/storage'

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: false,
  user: null,
  token: null,
  error: null,
}

// 异步 thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials)
      const { user, token } = response.data
      
      // 保存 token 到本地存储
      tokenStorage.setToken(token)
      
      return { user, token }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '登录失败')
    }
  }
)

export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData: RegisterData, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(userData)
      const { user, token } = response.data
      
      // 保存 token 到本地存储
      tokenStorage.setToken(token)
      
      return { user, token }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '注册失败')
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authAPI.logout()
      
      // 清除本地存储的 token
      tokenStorage.removeToken()
      
      return null
    } catch (error: any) {
      // 即使退出登录失败，也要清除本地 token
      tokenStorage.removeToken()
      return rejectWithValue(error.response?.data?.error?.message || '退出登录失败')
    }
  }
)

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.refreshToken()
      const { token } = response.data
      
      // 更新本地存储的 token
      tokenStorage.setToken(token)
      
      return { token }
    } catch (error: any) {
      // 刷新失败，清除本地 token
      tokenStorage.removeToken()
      return rejectWithValue(error.response?.data?.error?.message || '刷新令牌失败')
    }
  }
)

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.getCurrentUser()
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取用户信息失败')
    }
  }
)

export const updateUserProfile = createAsyncThunk(
  'auth/updateProfile',
  async (userData: Partial<User>, { rejectWithValue }) => {
    try {
      const response = await authAPI.updateProfile(userData)
      return response.data
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '更新用户信息失败')
    }
  }
)

// 创建 slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 清除错误
    clearError: (state) => {
      state.error = null
    },
    
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    
    // 初始化认证状态（从本地存储恢复）
    initializeAuth: (state) => {
      const token = tokenStorage.getToken()
      if (token) {
        state.token = token
        state.isAuthenticated = true
      }
    },
    
    // 重置认证状态
    resetAuth: (state) => {
      state.isAuthenticated = false
      state.user = null
      state.token = null
      state.error = null
      state.isLoading = false
    },
  },
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.isAuthenticated = true
        state.user = action.payload.user
        state.token = action.payload.token
        state.error = null
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false
        state.isAuthenticated = false
        state.user = null
        state.token = null
        state.error = action.payload as string
      })

    // 注册
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.isAuthenticated = true
        state.user = action.payload.user
        state.token = action.payload.token
        state.error = null
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false
        state.isAuthenticated = false
        state.user = null
        state.token = null
        state.error = action.payload as string
      })

    // 退出登录
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false
        state.isAuthenticated = false
        state.user = null
        state.token = null
        state.error = null
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false
        state.isAuthenticated = false
        state.user = null
        state.token = null
        state.error = action.payload as string
      })

    // 刷新令牌
    builder
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.token
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isAuthenticated = false
        state.user = null
        state.token = null
      })

    // 获取当前用户
    builder
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload
        state.isAuthenticated = true
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false
        state.isAuthenticated = false
        state.user = null
        state.token = null
        state.error = action.payload as string
      })

    // 更新用户信息
    builder
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.user = action.payload
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.error = action.payload as string
      })
  },
})

// 导出 actions
export const { clearError, setLoading, initializeAuth, resetAuth } = authSlice.actions

// 导出 reducer
export default authSlice.reducer

// 选择器
export const selectAuth = (state: { auth: AuthState }) => state.auth
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated
export const selectCurrentUser = (state: { auth: AuthState }) => state.auth.user
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.isLoading
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error
