import { configureStore } from '@reduxjs/toolkit'
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux'

// 导入 reducers
import authReducer from './slices/authSlice'
import documentReducer from './slices/documentSlice'
import collaborationReducer from './slices/collaborationSlice'
import uiReducer from './slices/uiSlice'
import notificationReducer from './slices/notificationSlice'

// 配置 store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    document: documentReducer,
    collaboration: collaborationReducer,
    ui: uiReducer,
    notification: notificationReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略这些 action types 的序列化检查
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
        // 忽略这些 paths 的序列化检查
        ignoredPaths: ['register'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

// 导出类型
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// 导出类型化的 hooks
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector

// 默认导出 store
export default store
