{"name": "ai-doc-frontend", "version": "1.0.0", "description": "智能文档系统前端应用", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/extension-collaboration": "^2.1.13", "@tiptap/extension-collaboration-cursor": "^2.1.13", "@tiptap/extension-highlight": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-table": "^2.1.13", "@tiptap/extension-table-row": "^2.1.13", "@tiptap/extension-table-cell": "^2.1.13", "@tiptap/extension-table-header": "^2.1.13", "@tiptap/extension-task-list": "^2.1.13", "@tiptap/extension-task-item": "^2.1.13", "@tiptap/extension-mention": "^2.1.13", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-character-count": "^2.1.13", "@tiptap/pm": "^2.1.13", "yjs": "^13.6.8", "y-websocket": "^1.5.0", "y-indexeddb": "^9.0.12", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.2.3", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/file-saver": "^2.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "jsdom": "^23.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.0", "vite-bundle-analyzer": "^0.7.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}