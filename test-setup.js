#!/usr/bin/env node

/**
 * 项目设置验证脚本
 * 验证项目结构和基础配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`,
};

console.log(colors.bold('🔍 AI-Doc 项目结构验证'));
console.log('='.repeat(50));

// 检查必需的文件和目录
const requiredStructure = [
  // 根目录文件
  'package.json',
  '.env.example',
  '.gitignore',
  'docker-compose.yml',
  'README.md',
  'requirements.md',
  'ARCHITECTURE.md',
  'DATA_STRUCTURE.md',
  'TECH_SELECTION.md',
  
  // 前端目录
  'frontend/',
  'frontend/package.json',
  'frontend/vite.config.ts',
  'frontend/tsconfig.json',
  'frontend/index.html',
  'frontend/src/',
  'frontend/src/main.tsx',
  'frontend/src/App.tsx',
  'frontend/src/types/',
  'frontend/src/store/',
  'frontend/src/styles/',
  
  // 后端目录
  'backend/',
  'backend/package.json',
  'backend/tsconfig.json',
  'backend/src/',
  'backend/src/index.ts',
  'backend/prisma/',
  'backend/prisma/schema.prisma',
  'backend/Dockerfile',
  
  // AI 服务目录
  'ai-service/',
  'ai-service/requirements.txt',
  'ai-service/src/',
  'ai-service/src/main.py',
  'ai-service/src/config/',
  'ai-service/Dockerfile',
  
  // 文档目录
  'docs/',
  'docs/实现说明.md',
  
  // 脚本目录
  'scripts/',
  'scripts/setup.sh',
];

let passedChecks = 0;
let totalChecks = requiredStructure.length;

console.log(colors.blue('📁 检查项目结构...'));

requiredStructure.forEach((item) => {
  const itemPath = path.join(process.cwd(), item);
  const exists = fs.existsSync(itemPath);
  
  if (exists) {
    console.log(colors.green(`✅ ${item}`));
    passedChecks++;
  } else {
    console.log(colors.red(`❌ ${item}`));
  }
});

console.log('\n' + '='.repeat(50));

// 检查 package.json 配置
console.log(colors.blue('📦 检查 package.json 配置...'));

try {
  const rootPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // 检查工作区配置
  if (rootPackage.workspaces && Array.isArray(rootPackage.workspaces)) {
    console.log(colors.green('✅ 工作区配置正确'));
    passedChecks++;
  } else {
    console.log(colors.red('❌ 工作区配置缺失'));
  }
  totalChecks++;
  
  // 检查脚本配置
  const requiredScripts = ['dev', 'build', 'test', 'lint'];
  requiredScripts.forEach(script => {
    if (rootPackage.scripts && rootPackage.scripts[script]) {
      console.log(colors.green(`✅ 脚本 "${script}" 已配置`));
      passedChecks++;
    } else {
      console.log(colors.red(`❌ 脚本 "${script}" 缺失`));
    }
    totalChecks++;
  });
  
} catch (error) {
  console.log(colors.red('❌ 无法读取根目录 package.json'));
  totalChecks += 5;
}

// 检查前端配置
console.log(colors.blue('⚛️  检查前端配置...'));

try {
  const frontendPackage = JSON.parse(fs.readFileSync('frontend/package.json', 'utf8'));
  
  // 检查关键依赖
  const requiredDeps = ['react', '@tiptap/react', '@mui/material', 'yjs'];
  requiredDeps.forEach(dep => {
    if (frontendPackage.dependencies && frontendPackage.dependencies[dep]) {
      console.log(colors.green(`✅ 前端依赖 "${dep}" 已配置`));
      passedChecks++;
    } else {
      console.log(colors.red(`❌ 前端依赖 "${dep}" 缺失`));
    }
    totalChecks++;
  });
  
} catch (error) {
  console.log(colors.red('❌ 无法读取前端 package.json'));
  totalChecks += 4;
}

// 检查后端配置
console.log(colors.blue('🚀 检查后端配置...'));

try {
  const backendPackage = JSON.parse(fs.readFileSync('backend/package.json', 'utf8'));
  
  // 检查关键依赖
  const requiredDeps = ['express', 'prisma', 'socket.io', 'yjs'];
  requiredDeps.forEach(dep => {
    if (backendPackage.dependencies && backendPackage.dependencies[dep]) {
      console.log(colors.green(`✅ 后端依赖 "${dep}" 已配置`));
      passedChecks++;
    } else {
      console.log(colors.red(`❌ 后端依赖 "${dep}" 缺失`));
    }
    totalChecks++;
  });
  
} catch (error) {
  console.log(colors.red('❌ 无法读取后端 package.json'));
  totalChecks += 4;
}

// 检查 AI 服务配置
console.log(colors.blue('🤖 检查 AI 服务配置...'));

try {
  const requirements = fs.readFileSync('ai-service/requirements.txt', 'utf8');
  
  // 检查关键依赖
  const requiredDeps = ['fastapi', 'google-generativeai', 'uvicorn', 'pydantic'];
  requiredDeps.forEach(dep => {
    if (requirements.includes(dep)) {
      console.log(colors.green(`✅ AI 服务依赖 "${dep}" 已配置`));
      passedChecks++;
    } else {
      console.log(colors.red(`❌ AI 服务依赖 "${dep}" 缺失`));
    }
    totalChecks++;
  });
  
} catch (error) {
  console.log(colors.red('❌ 无法读取 AI 服务 requirements.txt'));
  totalChecks += 4;
}

// 检查 Docker 配置
console.log(colors.blue('🐳 检查 Docker 配置...'));

try {
  const dockerCompose = fs.readFileSync('docker-compose.yml', 'utf8');
  
  // 检查关键服务
  const requiredServices = ['postgres', 'redis', 'backend', 'frontend', 'ai-service'];
  requiredServices.forEach(service => {
    if (dockerCompose.includes(service + ':')) {
      console.log(colors.green(`✅ Docker 服务 "${service}" 已配置`));
      passedChecks++;
    } else {
      console.log(colors.red(`❌ Docker 服务 "${service}" 缺失`));
    }
    totalChecks++;
  });
  
} catch (error) {
  console.log(colors.red('❌ 无法读取 docker-compose.yml'));
  totalChecks += 5;
}

// 输出结果
console.log('\n' + '='.repeat(50));
console.log(colors.bold('📊 验证结果'));

const successRate = (passedChecks / totalChecks * 100).toFixed(1);

if (passedChecks === totalChecks) {
  console.log(colors.green(`🎉 所有检查通过! (${passedChecks}/${totalChecks})`));
  console.log(colors.green('✨ 项目结构配置完整，可以开始开发了！'));
} else if (successRate >= 80) {
  console.log(colors.yellow(`⚠️  大部分检查通过 (${passedChecks}/${totalChecks} - ${successRate}%)`));
  console.log(colors.yellow('🔧 请修复缺失的配置项'));
} else {
  console.log(colors.red(`❌ 检查失败 (${passedChecks}/${totalChecks} - ${successRate}%)`));
  console.log(colors.red('🚨 项目结构不完整，请检查配置'));
}

console.log('\n' + colors.blue('📚 下一步操作:'));
console.log('1. 复制 .env.example 为 .env 并配置环境变量');
console.log('2. 运行 ./scripts/setup.sh 安装依赖');
console.log('3. 使用 npm run dev 启动开发服务器');
console.log('4. 或使用 docker-compose up -d 启动 Docker 环境');

process.exit(passedChecks === totalChecks ? 0 : 1);
