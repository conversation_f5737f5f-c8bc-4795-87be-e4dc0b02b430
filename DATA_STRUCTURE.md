# 智能文档系统数据结构设计

## 1. 概述

本文档详细定义了智能文档系统的数据结构设计，基于 PostgreSQL 数据库和 TipTap/ProseMirror 编辑器的技术选型。数据结构设计遵循以下原则：

- **兼容性**: 与 TipTap/ProseMirror 的 JSON 格式完全兼容
- **扩展性**: 支持未来功能扩展和数据迁移
- **性能**: 优化查询性能和存储效率
- **一致性**: 保证数据完整性和事务一致性

## 2. 核心数据表设计

### 2.1 用户管理表

```sql
-- 用户基础信息表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, suspended
    preferences JSONB DEFAULT '{}', -- 用户偏好设置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- 用户会话表
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    device_info JSONB, -- 设备信息
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.2 文档管理表

```sql
-- 文档基础信息表
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(200), -- URL友好的标识符
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
    template_id UUID REFERENCES templates(id) ON DELETE SET NULL,
    
    -- 文档内容 (TipTap/ProseMirror JSON格式)
    content JSONB NOT NULL DEFAULT '{"type": "doc", "content": []}',
    
    -- 文档元数据
    metadata JSONB DEFAULT '{}', -- 自定义元数据
    tags TEXT[], -- 标签数组
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft', -- draft, published, archived, deleted
    visibility VARCHAR(20) DEFAULT 'private', -- private, shared, public
    
    -- 版本控制
    version INTEGER DEFAULT 1,
    
    -- 统计信息
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    reading_time_minutes INTEGER DEFAULT 0,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    
    -- 索引优化
    search_vector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('chinese', title), 'A') ||
        setweight(to_tsvector('chinese', COALESCE(content->>'text', '')), 'B')
    ) STORED
);

-- 文件夹表
CREATE TABLE folders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    parent_id UUID REFERENCES folders(id) ON DELETE CASCADE,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    color VARCHAR(7), -- 十六进制颜色代码
    icon VARCHAR(50), -- 图标名称
    position INTEGER DEFAULT 0, -- 排序位置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.3 协作和权限表

```sql
-- 文档协作者表
CREATE TABLE document_collaborators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL, -- owner, editor, commenter, viewer
    permissions JSONB DEFAULT '{}', -- 详细权限配置
    invited_by UUID REFERENCES users(id),
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(document_id, user_id)
);

-- 实时协作会话表
CREATE TABLE collaboration_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    cursor_position JSONB, -- 光标位置信息
    selection_range JSONB, -- 选择范围
    is_active BOOLEAN DEFAULT true,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.4 版本历史和变更追踪

```sql
-- 文档版本历史表
CREATE TABLE document_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content JSONB NOT NULL, -- 完整的文档内容快照
    content_diff JSONB, -- 与上一版本的差异
    title VARCHAR(500) NOT NULL,
    
    -- 变更信息
    changed_by UUID NOT NULL REFERENCES users(id),
    change_summary TEXT, -- 变更摘要
    change_type VARCHAR(50), -- manual_save, auto_save, collaboration, ai_suggestion
    
    -- 统计信息
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(document_id, version_number)
);

-- Y.js 协作状态存储表
CREATE TABLE yjs_documents (
    document_id UUID PRIMARY KEY REFERENCES documents(id) ON DELETE CASCADE,
    yjs_state BYTEA NOT NULL, -- Y.js 二进制状态数据
    last_update_vector BYTEA, -- 最后更新向量
    last_modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_modified_by UUID REFERENCES users(id)
);
```

## 3. TipTap/ProseMirror 数据结构

### 3.1 文档内容格式

```json
{
  "type": "doc",
  "content": [
    {
      "type": "heading",
      "attrs": {
        "level": 1,
        "id": "heading-1"
      },
      "content": [
        {
          "type": "text",
          "text": "文档标题"
        }
      ]
    },
    {
      "type": "paragraph",
      "content": [
        {
          "type": "text",
          "text": "这是一个段落，包含 "
        },
        {
          "type": "text",
          "marks": [
            {
              "type": "bold"
            }
          ],
          "text": "粗体文本"
        },
        {
          "type": "text",
          "text": " 和 "
        },
        {
          "type": "text",
          "marks": [
            {
              "type": "italic"
            }
          ],
          "text": "斜体文本"
        }
      ]
    },
    {
      "type": "bulletList",
      "content": [
        {
          "type": "listItem",
          "content": [
            {
              "type": "paragraph",
              "content": [
                {
                  "type": "text",
                  "text": "列表项 1"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "type": "table",
      "content": [
        {
          "type": "tableRow",
          "content": [
            {
              "type": "tableHeader",
              "attrs": {
                "colspan": 1,
                "rowspan": 1
              },
              "content": [
                {
                  "type": "paragraph",
                  "content": [
                    {
                      "type": "text",
                      "text": "表头"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 3.2 扩展节点类型

```json
{
  "type": "aiSuggestion",
  "attrs": {
    "suggestionId": "uuid",
    "type": "rewrite",
    "status": "pending",
    "confidence": 0.95
  },
  "content": [
    {
      "type": "paragraph",
      "content": [
        {
          "type": "text",
          "text": "AI建议的内容"
        }
      ]
    }
  ]
},
{
  "type": "comment",
  "attrs": {
    "commentId": "uuid",
    "authorId": "uuid",
    "resolved": false,
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "content": [
    {
      "type": "text",
      "text": "这是一个评论"
    }
  ]
}
```

## 4. 评论和任务管理表

```sql
-- 评论表
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE, -- 回复评论

    -- 评论内容
    content TEXT NOT NULL,
    content_json JSONB, -- 富文本评论内容

    -- 位置信息
    anchor_position JSONB, -- 在文档中的锚点位置
    selection_range JSONB, -- 选中的文本范围

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active', -- active, resolved, deleted
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 任务表
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,

    -- 任务内容
    title VARCHAR(500) NOT NULL,
    description TEXT,

    -- 位置信息
    anchor_position JSONB, -- 在文档中的位置

    -- 状态管理
    status VARCHAR(20) DEFAULT 'todo', -- todo, in_progress, completed, cancelled
    priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, urgent

    -- 时间管理
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 5. AI 功能相关表

```sql
-- AI 会话表
CREATE TABLE ai_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- 会话信息
    session_type VARCHAR(50) NOT NULL, -- chat, rewrite, summarize, proofread
    context JSONB, -- 会话上下文

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active', -- active, completed, cancelled

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI 消息表
CREATE TABLE ai_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES ai_sessions(id) ON DELETE CASCADE,

    -- 消息内容
    role VARCHAR(20) NOT NULL, -- user, assistant, system
    content TEXT NOT NULL,
    content_json JSONB, -- 结构化内容

    -- 元数据
    metadata JSONB DEFAULT '{}', -- 模型信息、token使用等

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI 建议表
CREATE TABLE ai_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES ai_sessions(id) ON DELETE SET NULL,

    -- 建议内容
    suggestion_type VARCHAR(50) NOT NULL, -- rewrite, expand, summarize, proofread
    original_content JSONB NOT NULL, -- 原始内容
    suggested_content JSONB NOT NULL, -- 建议内容

    -- 位置信息
    position_range JSONB NOT NULL, -- 在文档中的位置范围

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, rejected, modified
    confidence_score DECIMAL(3,2), -- 置信度分数 0.00-1.00

    -- 用户反馈
    user_feedback TEXT,
    feedback_rating INTEGER CHECK (feedback_rating >= 1 AND feedback_rating <= 5),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    applied_at TIMESTAMP WITH TIME ZONE
);
```

## 6. 模板和导入导出表

```sql
-- 模板表
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100), -- 模板分类

    -- 模板内容
    content JSONB NOT NULL, -- TipTap JSON格式
    thumbnail_url TEXT, -- 模板缩略图

    -- 访问控制
    is_public BOOLEAN DEFAULT false,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- 使用统计
    usage_count INTEGER DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 导入导出任务表
CREATE TABLE import_export_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,

    -- 任务信息
    job_type VARCHAR(20) NOT NULL, -- import, export
    file_format VARCHAR(20) NOT NULL, -- docx, pdf, md, txt, html

    -- 文件信息
    original_filename VARCHAR(500),
    file_size BIGINT,
    file_url TEXT, -- 存储在对象存储中的URL

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    progress_percentage INTEGER DEFAULT 0,
    error_message TEXT,

    -- 处理结果
    result_data JSONB, -- 处理结果的元数据

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);
```

## 7. 索引和性能优化

```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- 文档表索引
CREATE INDEX idx_documents_owner_id ON documents(owner_id);
CREATE INDEX idx_documents_folder_id ON documents(folder_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_visibility ON documents(visibility);
CREATE INDEX idx_documents_created_at ON documents(created_at DESC);
CREATE INDEX idx_documents_updated_at ON documents(updated_at DESC);
CREATE INDEX idx_documents_search_vector ON documents USING gin(search_vector);
CREATE INDEX idx_documents_tags ON documents USING gin(tags);

-- 协作表索引
CREATE INDEX idx_document_collaborators_document_id ON document_collaborators(document_id);
CREATE INDEX idx_document_collaborators_user_id ON document_collaborators(user_id);
CREATE INDEX idx_collaboration_sessions_document_id ON collaboration_sessions(document_id);
CREATE INDEX idx_collaboration_sessions_user_id ON collaboration_sessions(user_id);
CREATE INDEX idx_collaboration_sessions_active ON collaboration_sessions(is_active, last_activity_at);

-- 版本历史索引
CREATE INDEX idx_document_versions_document_id ON document_versions(document_id, version_number DESC);
CREATE INDEX idx_document_versions_changed_by ON document_versions(changed_by);

-- 评论和任务索引
CREATE INDEX idx_comments_document_id ON comments(document_id);
CREATE INDEX idx_comments_author_id ON comments(author_id);
CREATE INDEX idx_comments_status ON comments(status);
CREATE INDEX idx_tasks_document_id ON tasks(document_id);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);

-- AI功能索引
CREATE INDEX idx_ai_sessions_document_id ON ai_sessions(document_id);
CREATE INDEX idx_ai_sessions_user_id ON ai_sessions(user_id);
CREATE INDEX idx_ai_messages_session_id ON ai_messages(session_id);
CREATE INDEX idx_ai_suggestions_document_id ON ai_suggestions(document_id);
CREATE INDEX idx_ai_suggestions_status ON ai_suggestions(status);
```

## 8. 数据完整性约束和触发器

```sql
-- 触发器：自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_folders_updated_at BEFORE UPDATE ON folders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 触发器：自动更新文档统计信息
CREATE OR REPLACE FUNCTION update_document_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 计算字数和字符数（简化版本，实际应用中可能需要更复杂的逻辑）
    NEW.word_count = (
        SELECT LENGTH(REGEXP_REPLACE(NEW.content::text, '[^\w\u4e00-\u9fff]+', ' ', 'g')) -
               LENGTH(REPLACE(REGEXP_REPLACE(NEW.content::text, '[^\w\u4e00-\u9fff]+', ' ', 'g'), ' ', '')) + 1
    );
    NEW.character_count = LENGTH(NEW.content::text);
    NEW.reading_time_minutes = GREATEST(1, NEW.word_count / 200); -- 假设每分钟阅读200字

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_document_stats_trigger BEFORE INSERT OR UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_document_stats();
```

## 9. 数据关系图

```mermaid
erDiagram
    users ||--o{ documents : owns
    users ||--o{ document_collaborators : collaborates
    users ||--o{ comments : creates
    users ||--o{ tasks : creates
    users ||--o{ ai_sessions : initiates

    documents ||--o{ document_versions : has_versions
    documents ||--o{ comments : contains
    documents ||--o{ tasks : contains
    documents ||--o{ ai_sessions : involves
    documents ||--o{ document_collaborators : shared_with
    documents }o--|| folders : organized_in
    documents }o--|| templates : based_on

    folders ||--o{ folders : contains

    ai_sessions ||--o{ ai_messages : contains
    ai_sessions ||--o{ ai_suggestions : generates

    comments ||--o{ comments : replies_to

    templates ||--o{ documents : instantiates
```

## 10. 最佳实践和注意事项

### 10.1 数据安全
- 所有敏感数据（如密码）必须加密存储
- 实施行级安全策略（RLS）保护用户数据
- 定期备份数据库并测试恢复流程

### 10.2 性能优化
- 合理使用 JSONB 索引提升查询性能
- 对大型文档考虑分页加载
- 实施缓存策略减少数据库压力

### 10.3 扩展性考虑
- 预留扩展字段（metadata, preferences等）
- 使用 UUID 作为主键便于分布式部署
- 设计支持水平分片的数据结构

### 10.4 数据迁移
- 版本化数据库模式变更
- 提供向后兼容的迁移脚本
- 支持 TipTap 内容格式的版本升级
