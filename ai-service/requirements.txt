# Web 框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP 客户端
httpx==0.25.2
aiohttp==3.9.1

# AI 和机器学习
google-generativeai==0.3.2
openai==1.3.7
anthropic==0.7.7
transformers==4.36.0
torch==2.1.1
numpy==1.24.4
scikit-learn==1.3.2

# 数据处理
pandas==2.1.4
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 数据库
asyncpg==0.29.0
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.13.1

# 文档处理
python-docx==1.1.0
PyPDF2==3.0.1
markdown==3.5.1
beautifulsoup4==4.12.2
lxml==4.9.3
pypandoc==1.12

# 工具库
python-dotenv==1.0.0
python-json-logger==2.0.7
structlog==23.2.0
rich==13.7.0
typer==0.9.0
click==8.1.7

# 验证和序列化
marshmallow==3.20.1
cerberus==1.3.5

# 异步任务
celery==5.3.4
redis==5.0.1

# 监控和日志
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
safety==2.3.5

# 开发工具
pre-commit==3.6.0
watchdog==3.0.0

# 部署
gunicorn==21.2.0
docker==6.1.3

# 其他
Pillow==10.1.0
python-magic==0.4.27
chardet==5.2.0
langdetect==1.0.9
jieba==0.42.1
emoji==2.8.0
