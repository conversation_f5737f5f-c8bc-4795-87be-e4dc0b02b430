"""
AI 服务主入口文件
提供智能写作、改写、校对等 AI 功能的 FastAPI 服务
"""

import asyncio
import logging
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from src.config.settings import settings
from src.config.logging import setup_logging
from src.api.routes import api_router
from src.services.ai_service import AIService
from src.utils.exceptions import AIServiceException
from src.middleware.auth import AuthMiddleware
from src.middleware.rate_limit import RateLimitMiddleware

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 AI 服务启动中...")
    
    try:
        # 初始化 AI 服务
        ai_service = AIService()
        await ai_service.initialize()
        app.state.ai_service = ai_service
        
        logger.info("✅ AI 服务初始化完成")
        logger.info(f"🌍 环境: {settings.ENVIRONMENT}")
        logger.info(f"🔑 AI 模型: {settings.AI_MODEL}")
        
    except Exception as e:
        logger.error(f"❌ AI 服务初始化失败: {e}")
        sys.exit(1)
    
    yield
    
    # 关闭时执行
    logger.info("🛑 AI 服务关闭中...")
    
    try:
        if hasattr(app.state, 'ai_service'):
            await app.state.ai_service.cleanup()
        logger.info("✅ AI 服务已优雅关闭")
    except Exception as e:
        logger.error(f"❌ AI 服务关闭时出错: {e}")


# 创建 FastAPI 应用
app = FastAPI(
    title="AI-Doc AI 服务",
    description="智能文档系统的 AI 服务，提供智能写作、改写、校对等功能",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan,
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(AuthMiddleware)
app.add_middleware(RateLimitMiddleware)


# 异常处理器
@app.exception_handler(AIServiceException)
async def ai_service_exception_handler(request: Request, exc: AIServiceException):
    """AI 服务异常处理器"""
    logger.error(f"AI 服务异常: {exc.message}", extra={"error_code": exc.error_code})
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details,
            },
            "timestamp": exc.timestamp.isoformat(),
            "request_id": getattr(request.state, "request_id", None),
        },
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.warning(f"请求验证失败: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "请求参数验证失败",
                "details": exc.errors(),
            },
            "timestamp": asyncio.get_event_loop().time(),
            "request_id": getattr(request.state, "request_id", None),
        },
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP 异常处理器"""
    logger.error(f"HTTP 异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": f"HTTP_{exc.status_code}",
                "message": exc.detail,
            },
            "timestamp": asyncio.get_event_loop().time(),
            "request_id": getattr(request.state, "request_id", None),
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__}: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "服务器内部错误",
            },
            "timestamp": asyncio.get_event_loop().time(),
            "request_id": getattr(request.state, "request_id", None),
        },
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查 AI 服务状态
        ai_service = app.state.ai_service
        is_healthy = await ai_service.health_check()
        
        if not is_healthy:
            raise HTTPException(status_code=503, detail="AI 服务不可用")
        
        return {
            "success": True,
            "message": "AI 服务运行正常",
            "timestamp": asyncio.get_event_loop().time(),
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT,
            "ai_model": settings.AI_MODEL,
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI-Doc AI 服务",
        "version": "1.0.0",
        "docs": "/docs" if settings.ENVIRONMENT == "development" else None,
    }


# 包含 API 路由
app.include_router(api_router, prefix="/api/v1")


# 启动服务器
if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        use_colors=True,
    )
