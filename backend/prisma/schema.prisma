// Prisma 数据库模式定义
// 智能文档系统的完整数据模型

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  displayName String
  avatarUrl   String?
  status      UserStatus @default(ACTIVE)
  preferences Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?

  // 密码相关
  passwordHash String
  emailVerified Boolean @default(false)
  emailVerificationToken String?
  passwordResetToken String?
  passwordResetExpires DateTime?

  // 关联关系
  ownedDocuments    Document[] @relation("DocumentOwner")
  collaborations    Collaborator[]
  comments          Comment[]
  tasks             Task[] @relation("TaskCreator")
  assignedTasks     Task[] @relation("TaskAssignee")
  aiSessions        AISession[]
  templates         Template[]
  folders           Folder[]
  notifications     Notification[]
  
  @@map("users")
}

// 用户状态枚举
enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

// 文档模型
model Document {
  id          String   @id @default(cuid())
  title       String
  slug        String?
  content     Json     @default("{}")
  metadata    Json     @default("{}")
  tags        String[] @default([])
  status      DocumentStatus @default(DRAFT)
  visibility  DocumentVisibility @default(PRIVATE)
  version     Int      @default(1)
  wordCount   Int      @default(0)
  characterCount Int   @default(0)
  readingTimeMinutes Int @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?

  // 外键关系
  ownerId     String
  owner       User     @relation("DocumentOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  folderId    String?
  folder      Folder?  @relation(fields: [folderId], references: [id], onDelete: SetNull)
  templateId  String?
  template    Template? @relation(fields: [templateId], references: [id], onDelete: SetNull)

  // 关联关系
  versions      DocumentVersion[]
  collaborators Collaborator[]
  comments      Comment[]
  tasks         Task[]
  aiSessions    AISession[]
  
  @@map("documents")
}

// 文档状态枚举
enum DocumentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  DELETED
}

// 文档可见性枚举
enum DocumentVisibility {
  PRIVATE
  SHARED
  PUBLIC
}

// 文档版本模型
model DocumentVersion {
  id            String   @id @default(cuid())
  versionNumber Int
  content       Json
  contentDiff   Json?
  title         String
  changeSummary String?
  changeType    VersionChangeType @default(MANUAL_SAVE)
  wordCount     Int      @default(0)
  characterCount Int     @default(0)
  createdAt     DateTime @default(now())

  // 外键关系
  documentId    String
  document      Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  changedById   String
  changedBy     User     @relation(fields: [changedById], references: [id])

  @@unique([documentId, versionNumber])
  @@map("document_versions")
}

// 版本变更类型枚举
enum VersionChangeType {
  MANUAL_SAVE
  AUTO_SAVE
  COLLABORATION
  AI_SUGGESTION
}

// 文件夹模型
model Folder {
  id        String   @id @default(cuid())
  name      String
  color     String?
  icon      String?
  position  Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 外键关系
  parentId  String?
  parent    Folder?  @relation("FolderHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  ownerId   String
  owner     User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // 关联关系
  children  Folder[] @relation("FolderHierarchy")
  documents Document[]

  @@map("folders")
}

// 协作者模型
model Collaborator {
  id          String   @id @default(cuid())
  role        CollaboratorRole @default(VIEWER)
  permissions Json     @default("{}")
  invitedAt   DateTime @default(now())
  acceptedAt  DateTime?

  // 外键关系
  documentId  String
  document    Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  invitedById String?
  invitedBy   User?    @relation(fields: [invitedById], references: [id])

  @@unique([documentId, userId])
  @@map("collaborators")
}

// 协作者角色枚举
enum CollaboratorRole {
  OWNER
  EDITOR
  COMMENTER
  VIEWER
}

// 协作会话模型
model CollaborationSession {
  id            String   @id @default(cuid())
  sessionToken  String   @unique
  cursorPosition Json?
  selectionRange Json?
  isActive      Boolean  @default(true)
  lastActivityAt DateTime @default(now())
  createdAt     DateTime @default(now())

  // 外键关系
  documentId    String
  userId        String
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("collaboration_sessions")
}

// 评论模型
model Comment {
  id             String   @id @default(cuid())
  content        String
  contentJson    Json?
  anchorPosition Json?
  selectionRange Json?
  status         CommentStatus @default(ACTIVE)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  resolvedAt     DateTime?

  // 外键关系
  documentId     String
  document       Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  authorId       String
  author         User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  parentId       String?
  parent         Comment? @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  resolvedById   String?
  resolvedBy     User?    @relation(fields: [resolvedById], references: [id])

  // 关联关系
  replies        Comment[] @relation("CommentReplies")

  @@map("comments")
}

// 评论状态枚举
enum CommentStatus {
  ACTIVE
  RESOLVED
  DELETED
}

// 任务模型
model Task {
  id             String   @id @default(cuid())
  title          String
  description    String?
  anchorPosition Json?
  status         TaskStatus @default(TODO)
  priority       TaskPriority @default(MEDIUM)
  dueDate        DateTime?
  completedAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 外键关系
  documentId     String
  document       Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User     @relation("TaskCreator", fields: [createdById], references: [id], onDelete: Cascade)
  assignedToId   String?
  assignedTo     User?    @relation("TaskAssignee", fields: [assignedToId], references: [id])

  @@map("tasks")
}

// 任务状态枚举
enum TaskStatus {
  TODO
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// 任务优先级枚举
enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// AI 会话模型
model AISession {
  id          String   @id @default(cuid())
  sessionType AISessionType
  context     Json?
  status      AISessionStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 外键关系
  documentId  String?
  document    Document? @relation(fields: [documentId], references: [id], onDelete: Cascade)
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 关联关系
  messages    AIMessage[]
  suggestions AISuggestion[]

  @@map("ai_sessions")
}

// AI 会话类型枚举
enum AISessionType {
  CHAT
  REWRITE
  SUMMARIZE
  PROOFREAD
  EXPAND
  TRANSLATE
}

// AI 会话状态枚举
enum AISessionStatus {
  ACTIVE
  COMPLETED
  CANCELLED
}

// AI 消息模型
model AIMessage {
  id          String   @id @default(cuid())
  role        AIMessageRole
  content     String
  contentJson Json?
  metadata    Json?
  createdAt   DateTime @default(now())

  // 外键关系
  sessionId   String
  session     AISession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("ai_messages")
}

// AI 消息角色枚举
enum AIMessageRole {
  USER
  ASSISTANT
  SYSTEM
}

// AI 建议模型
model AISuggestion {
  id               String   @id @default(cuid())
  suggestionType   AISuggestionType
  originalContent  Json
  suggestedContent Json
  positionRange    Json
  status           AISuggestionStatus @default(PENDING)
  confidenceScore  Float?
  userFeedback     String?
  feedbackRating   Int?
  createdAt        DateTime @default(now())
  appliedAt        DateTime?

  // 外键关系
  documentId       String?
  userId           String
  sessionId        String?
  session          AISession? @relation(fields: [sessionId], references: [id], onDelete: SetNull)

  @@map("ai_suggestions")
}

// AI 建议类型枚举
enum AISuggestionType {
  REWRITE
  EXPAND
  SUMMARIZE
  PROOFREAD
  TRANSLATE
  FORMAT
}

// AI 建议状态枚举
enum AISuggestionStatus {
  PENDING
  ACCEPTED
  REJECTED
  MODIFIED
}

// 模板模型
model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String?
  content     Json
  thumbnailUrl String?
  isPublic    Boolean  @default(false)
  usageCount  Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 外键关系
  createdById String
  createdBy   User     @relation(fields: [createdById], references: [id], onDelete: Cascade)

  // 关联关系
  documents   Document[]

  @@map("templates")
}

// 导入导出任务模型
model ImportExportJob {
  id                String   @id @default(cuid())
  jobType           ImportExportType
  fileFormat        FileFormat
  originalFilename  String?
  fileSize          Int?
  fileUrl           String?
  status            JobStatus @default(PENDING)
  progressPercentage Int     @default(0)
  errorMessage      String?
  resultData        Json?
  createdAt         DateTime @default(now())
  completedAt       DateTime?

  // 外键关系
  userId            String
  documentId        String?

  @@map("import_export_jobs")
}

// 导入导出类型枚举
enum ImportExportType {
  IMPORT
  EXPORT
}

// 文件格式枚举
enum FileFormat {
  DOCX
  PDF
  MD
  TXT
  HTML
  JSON
}

// 任务状态枚举
enum JobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

// 通知模型
model Notification {
  id        String   @id @default(cuid())
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  // 外键关系
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// 通知类型枚举
enum NotificationType {
  MENTION
  COMMENT
  COLLABORATION
  TASK_ASSIGNED
  TASK_COMPLETED
  DOCUMENT_SHARED
  SYSTEM
}
