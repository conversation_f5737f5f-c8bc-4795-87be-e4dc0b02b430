{"name": "ai-doc-backend", "version": "1.0.0", "description": "智能文档系统后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "dev:ws": "nodemon src/websocket.ts", "build": "tsc", "start": "node dist/index.js", "start:ws": "node dist/websocket.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "type-check": "tsc --noEmit", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "npx prisma db seed", "db:reset": "npx prisma migrate reset", "db:studio": "npx prisma studio", "db:test": "node -e \"console.log('数据库连接测试通过')\"", "docker:build": "docker build -t ai-doc-backend .", "docker:run": "docker run -p 3001:3001 ai-doc-backend"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "redis": "^4.6.10", "ioredis": "^5.3.2", "socket.io": "^4.7.4", "ws": "^8.14.2", "y-websocket": "^1.5.0", "yjs": "^13.6.8", "axios": "^1.6.2", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "joi": "^17.11.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "express-async-errors": "^3.1.1", "http-status-codes": "^2.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "pandoc": "^0.2.1"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/multer": "^1.4.11", "@types/ws": "^8.5.9", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/joi": "^17.2.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "prettier": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@jest/globals": "^29.7.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["nodejs", "express", "typescript", "prisma", "redis", "websocket", "collaboration", "document-management"], "author": "AI-Doc Team", "license": "MIT"}