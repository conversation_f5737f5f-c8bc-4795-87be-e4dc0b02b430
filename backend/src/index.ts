import 'express-async-errors'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import { createServer } from 'http'

import { config } from '@config/index'
import { logger } from '@utils/logger'
import { errorHandler } from '@middleware/errorHandler'
import { notFoundHandler } from '@middleware/notFoundHandler'
import { authMiddleware } from '@middleware/auth'
import { validateRequest } from '@middleware/validation'

// 导入路由
import authRoutes from '@routes/auth'
import userRoutes from '@routes/user'
import documentRoutes from '@routes/document'
import folderRoutes from '@routes/folder'
import collaborationRoutes from '@routes/collaboration'
import commentRoutes from '@routes/comment'
import taskRoutes from '@routes/task'
import templateRoutes from '@routes/template'
import aiRoutes from '@routes/ai'
import uploadRoutes from '@routes/upload'

// 导入数据库连接
import { prisma } from '@config/database'
import { redisClient } from '@config/redis'

// 创建 Express 应用
const app = express()
const server = createServer(app)

// 基础中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"],
    },
  },
}))

app.use(cors({
  origin: config.cors.origin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}))

app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 日志中间件
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim())
  }
}))

// 速率限制
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: '请求过于频繁，请稍后再试',
    },
  },
  standardHeaders: true,
  legacyHeaders: false,
})
app.use('/api', limiter)

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`
    
    // 检查 Redis 连接
    await redisClient.ping()
    
    res.json({
      success: true,
      message: '服务运行正常',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.nodeEnv,
      uptime: process.uptime(),
    })
  } catch (error) {
    logger.error('健康检查失败:', error)
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: '服务不可用',
      },
      timestamp: new Date().toISOString(),
    })
  }
})

// API 路由
app.use('/api/auth', authRoutes)
app.use('/api/users', authMiddleware, userRoutes)
app.use('/api/documents', authMiddleware, documentRoutes)
app.use('/api/folders', authMiddleware, folderRoutes)
app.use('/api/collaboration', authMiddleware, collaborationRoutes)
app.use('/api/comments', authMiddleware, commentRoutes)
app.use('/api/tasks', authMiddleware, taskRoutes)
app.use('/api/templates', authMiddleware, templateRoutes)
app.use('/api/ai', authMiddleware, aiRoutes)
app.use('/api/upload', authMiddleware, uploadRoutes)

// API 文档路由 (仅在开发环境)
if (config.nodeEnv === 'development') {
  const swaggerJsdoc = require('swagger-jsdoc')
  const swaggerUi = require('swagger-ui-express')
  
  const swaggerOptions = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'AI-Doc API',
        version: '1.0.0',
        description: '智能文档系统 API 文档',
      },
      servers: [
        {
          url: `http://localhost:${config.port}`,
          description: '开发服务器',
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
    },
    apis: ['./src/routes/*.ts', './src/controllers/*.ts'],
  }
  
  const specs = swaggerJsdoc(swaggerOptions)
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs))
}

// 错误处理中间件
app.use(notFoundHandler)
app.use(errorHandler)

// 优雅关闭处理
const gracefulShutdown = async (signal: string) => {
  logger.info(`收到 ${signal} 信号，开始优雅关闭...`)
  
  server.close(async () => {
    logger.info('HTTP 服务器已关闭')
    
    try {
      // 关闭数据库连接
      await prisma.$disconnect()
      logger.info('数据库连接已关闭')
      
      // 关闭 Redis 连接
      await redisClient.quit()
      logger.info('Redis 连接已关闭')
      
      logger.info('应用已优雅关闭')
      process.exit(0)
    } catch (error) {
      logger.error('优雅关闭过程中出现错误:', error)
      process.exit(1)
    }
  })
  
  // 强制关闭超时
  setTimeout(() => {
    logger.error('强制关闭应用')
    process.exit(1)
  }, 10000)
}

// 监听关闭信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
process.on('SIGINT', () => gracefulShutdown('SIGINT'))

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的 Promise 拒绝:', reason, 'at:', promise)
  process.exit(1)
})

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    await prisma.$connect()
    logger.info('数据库连接成功')
    
    // 测试 Redis 连接
    await redisClient.ping()
    logger.info('Redis 连接成功')
    
    // 启动 HTTP 服务器
    server.listen(config.port, () => {
      logger.info(`🚀 服务器运行在端口 ${config.port}`)
      logger.info(`📚 API 文档: http://localhost:${config.port}/api-docs`)
      logger.info(`🏥 健康检查: http://localhost:${config.port}/health`)
      logger.info(`🌍 环境: ${config.nodeEnv}`)
    })
  } catch (error) {
    logger.error('服务器启动失败:', error)
    process.exit(1)
  }
}

// 启动应用
startServer()

export { app, server }
