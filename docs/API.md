# API 接口文档

## 1. 概述

智能文档系统提供 RESTful API 接口，支持文档管理、用户认证、实时协作和 AI 功能。所有 API 接口都遵循 REST 设计原则，使用 JSON 格式进行数据交换。

### 基础信息

- **基础 URL**: `https://api.your-domain.com/v1`
- **认证方式**: JWT <PERSON>
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid"
}
```

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid"
}
```

## 2. 认证接口

### 2.1 用户注册

```http
POST /auth/register
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "displayName": "用户名"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "username",
      "displayName": "用户名",
      "avatarUrl": null,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "token": "jwt-token",
    "expiresIn": "7d"
  }
}
```

### 2.2 用户登录

```http
POST /auth/login
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 2.3 刷新令牌

```http
POST /auth/refresh
```

**请求头**:
```
Authorization: Bearer <refresh-token>
```

### 2.4 用户登出

```http
POST /auth/logout
```

**请求头**:
```
Authorization: Bearer <access-token>
```

## 3. 用户管理接口

### 3.1 获取当前用户信息

```http
GET /users/me
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "username": "username",
    "displayName": "用户名",
    "avatarUrl": "https://example.com/avatar.jpg",
    "preferences": {
      "theme": "light",
      "language": "zh-CN"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "lastLoginAt": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 更新用户信息

```http
PUT /users/me
```

**请求体**:
```json
{
  "displayName": "新用户名",
  "avatarUrl": "https://example.com/new-avatar.jpg",
  "preferences": {
    "theme": "dark",
    "language": "en-US"
  }
}
```

### 3.3 修改密码

```http
PUT /users/me/password
```

**请求体**:
```json
{
  "currentPassword": "old-password",
  "newPassword": "new-password"
}
```

## 4. 文档管理接口

### 4.1 创建文档

```http
POST /documents
```

**请求体**:
```json
{
  "title": "新文档标题",
  "content": {
    "type": "doc",
    "content": []
  },
  "folderId": "uuid",
  "templateId": "uuid",
  "visibility": "private",
  "tags": ["标签1", "标签2"]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "新文档标题",
    "slug": "new-document",
    "content": {
      "type": "doc",
      "content": []
    },
    "ownerId": "uuid",
    "folderId": "uuid",
    "status": "draft",
    "visibility": "private",
    "version": 1,
    "wordCount": 0,
    "characterCount": 0,
    "readingTimeMinutes": 0,
    "tags": ["标签1", "标签2"],
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 4.2 获取文档列表

```http
GET /documents
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `folderId`: 文件夹ID
- `status`: 文档状态 (draft, published, archived)
- `visibility`: 可见性 (private, shared, public)
- `search`: 搜索关键词
- `tags`: 标签过滤
- `sortBy`: 排序字段 (createdAt, updatedAt, title)
- `sortOrder`: 排序方向 (asc, desc)

**响应**:
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "uuid",
        "title": "文档标题",
        "slug": "document-slug",
        "ownerId": "uuid",
        "owner": {
          "id": "uuid",
          "displayName": "用户名",
          "avatarUrl": "https://example.com/avatar.jpg"
        },
        "folderId": "uuid",
        "status": "draft",
        "visibility": "private",
        "version": 1,
        "wordCount": 100,
        "characterCount": 500,
        "readingTimeMinutes": 1,
        "tags": ["标签1"],
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 4.3 获取文档详情

```http
GET /documents/:id
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "文档标题",
    "slug": "document-slug",
    "content": {
      "type": "doc",
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "文档内容"
            }
          ]
        }
      ]
    },
    "ownerId": "uuid",
    "owner": {
      "id": "uuid",
      "displayName": "用户名",
      "avatarUrl": "https://example.com/avatar.jpg"
    },
    "folderId": "uuid",
    "folder": {
      "id": "uuid",
      "name": "文件夹名称"
    },
    "status": "draft",
    "visibility": "private",
    "version": 1,
    "wordCount": 100,
    "characterCount": 500,
    "readingTimeMinutes": 1,
    "tags": ["标签1"],
    "metadata": {},
    "collaborators": [
      {
        "userId": "uuid",
        "user": {
          "id": "uuid",
          "displayName": "协作者",
          "avatarUrl": "https://example.com/avatar.jpg"
        },
        "role": "editor",
        "invitedAt": "2024-01-01T00:00:00Z",
        "acceptedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "publishedAt": null
  }
}
```

### 4.4 更新文档

```http
PUT /documents/:id
```

**请求体**:
```json
{
  "title": "更新的标题",
  "content": {
    "type": "doc",
    "content": []
  },
  "status": "published",
  "visibility": "public",
  "tags": ["新标签"]
}
```

### 4.5 删除文档

```http
DELETE /documents/:id
```

**响应**:
```json
{
  "success": true,
  "message": "文档已删除"
}
```

## 5. 文件夹管理接口

### 5.1 创建文件夹

```http
POST /folders
```

**请求体**:
```json
{
  "name": "新文件夹",
  "parentId": "uuid",
  "color": "#FF5722",
  "icon": "folder"
}
```

### 5.2 获取文件夹列表

```http
GET /folders
```

**查询参数**:
- `parentId`: 父文件夹ID (不传则获取根文件夹)
- `includeDocuments`: 是否包含文档 (默认: false)

### 5.3 更新文件夹

```http
PUT /folders/:id
```

### 5.4 删除文件夹

```http
DELETE /folders/:id
```

## 6. 协作管理接口

### 6.1 邀请协作者

```http
POST /documents/:id/collaborators
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "role": "editor"
}
```

### 6.2 获取协作者列表

```http
GET /documents/:id/collaborators
```

### 6.3 更新协作者权限

```http
PUT /documents/:id/collaborators/:userId
```

**请求体**:
```json
{
  "role": "commenter"
}
```

### 6.4 移除协作者

```http
DELETE /documents/:id/collaborators/:userId
```

## 7. 评论接口

### 7.1 创建评论

```http
POST /documents/:id/comments
```

**请求体**:
```json
{
  "content": "这是一个评论",
  "anchorPosition": {
    "from": 10,
    "to": 20
  },
  "parentId": "uuid"
}
```

### 7.2 获取评论列表

```http
GET /documents/:id/comments
```

### 7.3 更新评论

```http
PUT /comments/:id
```

### 7.4 删除评论

```http
DELETE /comments/:id
```

### 7.5 解决评论

```http
POST /comments/:id/resolve
```

## 8. 版本历史接口

### 8.1 获取版本列表

```http
GET /documents/:id/versions
```

### 8.2 获取特定版本

```http
GET /documents/:id/versions/:version
```

### 8.3 恢复到指定版本

```http
POST /documents/:id/versions/:version/restore
```
