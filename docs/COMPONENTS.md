# 前端组件设计文档

## 1. 概述

本文档详细描述了智能文档系统前端的组件架构设计，基于 React + TypeScript + TipTap 技术栈。组件设计遵循原子设计原则，确保可复用性、可维护性和一致性。

## 2. 组件架构

### 2.1 目录结构

```
src/
├── components/              # 组件目录
│   ├── atoms/              # 原子组件
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Avatar/
│   │   └── Icon/
│   ├── molecules/          # 分子组件
│   │   ├── SearchBox/
│   │   ├── UserMenu/
│   │   ├── DocumentCard/
│   │   └── CommentItem/
│   ├── organisms/          # 有机体组件
│   │   ├── Header/
│   │   ├── Sidebar/
│   │   ├── DocumentEditor/
│   │   └── CollaboratorPanel/
│   ├── templates/          # 模板组件
│   │   ├── DocumentLayout/
│   │   ├── DashboardLayout/
│   │   └── AuthLayout/
│   └── pages/              # 页面组件
│       ├── Dashboard/
│       ├── DocumentEdit/
│       ├── Login/
│       └── Settings/
├── hooks/                  # 自定义 Hooks
├── store/                  # Redux 状态管理
├── utils/                  # 工具函数
├── types/                  # TypeScript 类型定义
└── styles/                 # 样式文件
```

### 2.2 设计原则

- **原子设计**: 遵循原子设计方法论，构建可复用的组件层次
- **单一职责**: 每个组件只负责一个特定功能
- **可组合性**: 组件可以灵活组合形成更复杂的界面
- **类型安全**: 使用 TypeScript 确保类型安全
- **可访问性**: 遵循 WCAG 2.1 AA 标准

## 3. 原子组件 (Atoms)

### 3.1 Button 组件

```typescript
// components/atoms/Button/Button.tsx
import React from 'react';
import { ButtonProps } from './Button.types';
import { StyledButton } from './Button.styles';

/**
 * 通用按钮组件
 * 支持多种变体、尺寸和状态
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  startIcon,
  endIcon,
  onClick,
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {startIcon && <span className="start-icon">{startIcon}</span>}
      {loading ? <span className="loading-spinner" /> : children}
      {endIcon && <span className="end-icon">{endIcon}</span>}
    </StyledButton>
  );
};
```

```typescript
// components/atoms/Button/Button.types.ts
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}
```

### 3.2 Input 组件

```typescript
// components/atoms/Input/Input.tsx
import React, { forwardRef } from 'react';
import { InputProps } from './Input.types';
import { StyledInputWrapper, StyledInput, StyledLabel, StyledError } from './Input.styles';

/**
 * 通用输入框组件
 * 支持多种类型、验证状态和辅助文本
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  startAdornment,
  endAdornment,
  fullWidth = false,
  ...props
}, ref) => {
  return (
    <StyledInputWrapper fullWidth={fullWidth}>
      {label && <StyledLabel>{label}</StyledLabel>}
      <div className="input-container">
        {startAdornment && <span className="start-adornment">{startAdornment}</span>}
        <StyledInput
          ref={ref}
          hasError={!!error}
          hasStartAdornment={!!startAdornment}
          hasEndAdornment={!!endAdornment}
          {...props}
        />
        {endAdornment && <span className="end-adornment">{endAdornment}</span>}
      </div>
      {error && <StyledError>{error}</StyledError>}
      {helperText && !error && <span className="helper-text">{helperText}</span>}
    </StyledInputWrapper>
  );
});
```

### 3.3 Avatar 组件

```typescript
// components/atoms/Avatar/Avatar.tsx
import React from 'react';
import { AvatarProps } from './Avatar.types';
import { StyledAvatar } from './Avatar.styles';

/**
 * 用户头像组件
 * 支持图片、文字和图标显示
 */
export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 'medium',
  variant = 'circular',
  fallbackIcon,
  online,
  ...props
}) => {
  // 生成用户名首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <StyledAvatar size={size} variant={variant} {...props}>
      {src ? (
        <img src={src} alt={alt || name} />
      ) : name ? (
        <span className="initials">{getInitials(name)}</span>
      ) : (
        fallbackIcon || <span className="default-icon">👤</span>
      )}
      {online && <span className="online-indicator" />}
    </StyledAvatar>
  );
};
```

## 4. 分子组件 (Molecules)

### 4.1 SearchBox 组件

```typescript
// components/molecules/SearchBox/SearchBox.tsx
import React, { useState, useCallback, useMemo } from 'react';
import { Input } from '../../atoms/Input';
import { Button } from '../../atoms/Button';
import { Icon } from '../../atoms/Icon';
import { SearchBoxProps } from './SearchBox.types';
import { useDebounce } from '../../../hooks/useDebounce';

/**
 * 搜索框组件
 * 支持实时搜索、历史记录和建议
 */
export const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = '搜索文档...',
  onSearch,
  onClear,
  suggestions = [],
  recentSearches = [],
  loading = false,
  ...props
}) => {
  const [value, setValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  // 防抖处理搜索
  const debouncedValue = useDebounce(value, 300);
  
  React.useEffect(() => {
    if (debouncedValue && onSearch) {
      onSearch(debouncedValue);
    }
  }, [debouncedValue, onSearch]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    setShowSuggestions(newValue.length > 0);
  }, []);

  const handleClear = useCallback(() => {
    setValue('');
    setShowSuggestions(false);
    onClear?.();
  }, [onClear]);

  const filteredSuggestions = useMemo(() => {
    if (!value) return recentSearches;
    return suggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(value.toLowerCase())
    );
  }, [value, suggestions, recentSearches]);

  return (
    <div className="search-box" {...props}>
      <Input
        value={value}
        onChange={handleInputChange}
        placeholder={placeholder}
        startAdornment={
          loading ? (
            <Icon name="loading" spin />
          ) : (
            <Icon name="search" />
          )
        }
        endAdornment={
          value && (
            <Button
              variant="ghost"
              size="small"
              onClick={handleClear}
              aria-label="清除搜索"
            >
              <Icon name="close" />
            </Button>
          )
        }
        onFocus={() => setShowSuggestions(true)}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
      />
      
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="suggestions-dropdown">
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={index}
              className="suggestion-item"
              onClick={() => {
                setValue(suggestion);
                setShowSuggestions(false);
                onSearch?.(suggestion);
              }}
            >
              <Icon name="search" />
              <span>{suggestion}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

### 4.2 DocumentCard 组件

```typescript
// components/molecules/DocumentCard/DocumentCard.tsx
import React from 'react';
import { Avatar } from '../../atoms/Avatar';
import { Button } from '../../atoms/Button';
import { Icon } from '../../atoms/Icon';
import { DocumentCardProps } from './DocumentCard.types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

/**
 * 文档卡片组件
 * 显示文档基本信息和操作按钮
 */
export const DocumentCard: React.FC<DocumentCardProps> = ({
  document,
  onOpen,
  onShare,
  onDelete,
  onDuplicate,
  showActions = true,
  ...props
}) => {
  const {
    id,
    title,
    owner,
    updatedAt,
    wordCount,
    readingTimeMinutes,
    tags,
    collaborators,
    status
  } = document;

  const handleCardClick = () => {
    onOpen?.(id);
  };

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation(); // 防止触发卡片点击事件
    action();
  };

  return (
    <div className="document-card" onClick={handleCardClick} {...props}>
      <div className="card-header">
        <h3 className="document-title">{title}</h3>
        {showActions && (
          <div className="card-actions">
            <Button
              variant="ghost"
              size="small"
              onClick={(e) => handleActionClick(e, () => onShare?.(id))}
              aria-label="分享文档"
            >
              <Icon name="share" />
            </Button>
            <Button
              variant="ghost"
              size="small"
              onClick={(e) => handleActionClick(e, () => onDuplicate?.(id))}
              aria-label="复制文档"
            >
              <Icon name="copy" />
            </Button>
            <Button
              variant="ghost"
              size="small"
              onClick={(e) => handleActionClick(e, () => onDelete?.(id))}
              aria-label="删除文档"
            >
              <Icon name="delete" />
            </Button>
          </div>
        )}
      </div>

      <div className="card-content">
        <div className="document-meta">
          <span className="word-count">{wordCount} 字</span>
          <span className="reading-time">{readingTimeMinutes} 分钟阅读</span>
          <span className={`status status-${status}`}>{getStatusText(status)}</span>
        </div>

        {tags && tags.length > 0 && (
          <div className="document-tags">
            {tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="tag">
                {tag}
              </span>
            ))}
            {tags.length > 3 && (
              <span className="tag-more">+{tags.length - 3}</span>
            )}
          </div>
        )}
      </div>

      <div className="card-footer">
        <div className="owner-info">
          <Avatar
            src={owner.avatarUrl}
            name={owner.displayName}
            size="small"
          />
          <span className="owner-name">{owner.displayName}</span>
        </div>

        <div className="collaborators">
          {collaborators?.slice(0, 3).map((collaborator, index) => (
            <Avatar
              key={collaborator.userId}
              src={collaborator.user.avatarUrl}
              name={collaborator.user.displayName}
              size="small"
              style={{ marginLeft: index > 0 ? '-8px' : '0' }}
            />
          ))}
          {collaborators && collaborators.length > 3 && (
            <span className="collaborator-count">
              +{collaborators.length - 3}
            </span>
          )}
        </div>

        <div className="update-time">
          {formatDistanceToNow(new Date(updatedAt), {
            addSuffix: true,
            locale: zhCN
          })}
        </div>
      </div>
    </div>
  );
};

// 辅助函数：获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  };
  return statusMap[status as keyof typeof statusMap] || status;
};
```
