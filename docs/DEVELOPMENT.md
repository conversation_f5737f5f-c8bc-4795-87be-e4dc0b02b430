# 开发环境配置指南

## 1. 概述

本文档详细说明了智能文档系统的本地开发环境搭建流程，包括依赖安装、数据库配置、服务启动和开发工具配置。

## 2. 环境要求

### 2.1 必需软件

- **Node.js**: 18.0+ (推荐使用 nvm 管理版本)
- **Python**: 3.9+ (推荐使用 pyenv 管理版本)
- **PostgreSQL**: 14+
- **Redis**: 6.0+
- **Git**: 2.30+

### 2.2 推荐工具

- **IDE**: Visual Studio Code
- **终端**: iTerm2 (macOS) / Windows Terminal (Windows)
- **API 测试**: Postman 或 Insomnia
- **数据库管理**: pgAdmin 或 DBeaver
- **Redis 管理**: RedisInsight

## 3. 环境安装

### 3.1 macOS 环境

```bash
# 安装 Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 Node.js (使用 nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.zshrc
nvm install 18
nvm use 18
nvm alias default 18

# 安装 Python (使用 pyenv)
brew install pyenv
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.zshrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(pyenv init -)"' >> ~/.zshrc
source ~/.zshrc
pyenv install 3.9.16
pyenv global 3.9.16

# 安装数据库
brew install postgresql@14 redis
brew services start postgresql@14
brew services start redis

# 安装其他工具
brew install git
```

### 3.2 Ubuntu/Debian 环境

```bash
# 更新包管理器
sudo apt update && sudo apt upgrade -y

# 安装 Node.js (使用 NodeSource)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 Python
sudo apt install -y python3.9 python3.9-pip python3.9-venv

# 安装 PostgreSQL
sudo apt install -y postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 安装 Redis
sudo apt install -y redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 安装 Git
sudo apt install -y git
```

### 3.3 Windows 环境

```powershell
# 安装 Chocolatey
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装软件
choco install nodejs python postgresql redis-64 git

# 启动服务
net start postgresql-x64-14
net start redis
```

## 4. 项目设置

### 4.1 克隆项目

```bash
# 克隆项目
git clone https://github.com/your-org/ai-doc.git
cd ai-doc

# 查看项目结构
tree -L 2
```

### 4.2 数据库初始化

```bash
# 创建 PostgreSQL 数据库
sudo -u postgres psql
```

```sql
-- 在 PostgreSQL 中执行
CREATE DATABASE ai_doc_dev;
CREATE USER ai_doc_dev WITH ENCRYPTED PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE ai_doc_dev TO ai_doc_dev;
ALTER USER ai_doc_dev CREATEDB;
\q
```

```bash
# 测试 Redis 连接
redis-cli ping
# 应该返回 PONG
```

### 4.3 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env.development

# 编辑开发环境配置
vim .env.development
```

```bash
# .env.development 文件内容
NODE_ENV=development

# 数据库配置
DATABASE_URL=postgresql://ai_doc_dev:dev_password@localhost:5432/ai_doc_dev
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=dev_jwt_secret_key_for_development_only
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# AI 服务配置
GEMINI_API_KEY=your_gemini_api_key_here
AI_SERVICE_URL=http://localhost:8000

# 本地开发端口
FRONTEND_PORT=3000
BACKEND_PORT=3001
AI_SERVICE_PORT=8000
WS_PORT=3002

# 应用 URL
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
WS_URL=ws://localhost:3002

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# 开发工具
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_GRAPHQL_PLAYGROUND=true
```

## 5. 依赖安装

### 5.1 前端依赖

```bash
cd frontend
npm install

# 安装开发工具
npm install -D @types/node @types/react @types/react-dom
npm install -D eslint prettier husky lint-staged
npm install -D @testing-library/react @testing-library/jest-dom
npm install -D storybook @storybook/react
```

### 5.2 后端依赖

```bash
cd ../backend
npm install

# 安装开发工具
npm install -D nodemon ts-node @types/node @types/express
npm install -D jest supertest @types/jest @types/supertest
npm install -D eslint prettier typescript
```

### 5.3 AI 服务依赖

```bash
cd ../ai-service

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```

## 6. 数据库迁移

```bash
# 回到项目根目录
cd ..

# 运行数据库迁移
npm run db:migrate

# 填充测试数据
npm run db:seed

# 验证数据库连接
npm run db:test
```

## 7. 启动开发服务

### 7.1 使用 npm scripts

```bash
# 启动所有服务
npm run dev

# 或者分别启动各个服务
npm run dev:frontend    # 启动前端 (端口 3000)
npm run dev:backend     # 启动后端 (端口 3001)
npm run dev:ai          # 启动 AI 服务 (端口 8000)
npm run dev:ws          # 启动 WebSocket 服务 (端口 3002)
```

### 7.2 使用 Docker Compose (可选)

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

## 8. 开发工具配置

### 8.1 VS Code 配置

创建 `.vscode/settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "tailwindCSS.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

创建 `.vscode/extensions.json`:

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-python.python",
    "ms-python.flake8",
    "ms-python.black-formatter",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### 8.2 Git Hooks 配置

```bash
# 安装 husky
npx husky install

# 添加 pre-commit hook
npx husky add .husky/pre-commit "npm run lint-staged"

# 添加 commit-msg hook
npx husky add .husky/commit-msg "npm run commitlint"
```

创建 `.lintstagedrc.js`:

```javascript
module.exports = {
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
    'git add'
  ],
  '*.{css,scss,less}': [
    'prettier --write',
    'git add'
  ],
  '*.{md,json}': [
    'prettier --write',
    'git add'
  ],
  '*.py': [
    'black',
    'flake8',
    'git add'
  ]
};
```

## 9. 测试配置

### 9.1 前端测试

```bash
cd frontend

# 运行单元测试
npm test

# 运行测试覆盖率
npm run test:coverage

# 运行 E2E 测试
npm run test:e2e
```

### 9.2 后端测试

```bash
cd backend

# 运行单元测试
npm test

# 运行集成测试
npm run test:integration

# 运行测试覆盖率
npm run test:coverage
```

### 9.3 AI 服务测试

```bash
cd ai-service
source venv/bin/activate

# 运行测试
pytest

# 运行测试覆盖率
pytest --cov=src
```

## 10. 调试配置

### 10.1 VS Code 调试配置

创建 `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Frontend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/frontend/node_modules/.bin/react-scripts",
      "args": ["start"],
      "env": {
        "NODE_ENV": "development"
      },
      "cwd": "${workspaceFolder}/frontend"
    },
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/src/index.ts",
      "env": {
        "NODE_ENV": "development"
      },
      "runtimeArgs": ["-r", "ts-node/register"],
      "cwd": "${workspaceFolder}/backend"
    },
    {
      "name": "Debug AI Service",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/ai-service/src/main.py",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/ai-service"
      },
      "cwd": "${workspaceFolder}/ai-service"
    }
  ]
}
```

## 11. 常见问题

### 11.1 端口冲突

```bash
# 查看端口占用
lsof -i :3000
lsof -i :3001

# 杀死进程
kill -9 <PID>
```

### 11.2 数据库连接问题

```bash
# 检查 PostgreSQL 状态
sudo systemctl status postgresql

# 重启 PostgreSQL
sudo systemctl restart postgresql

# 检查连接
psql -h localhost -U ai_doc_dev -d ai_doc_dev
```

### 11.3 依赖安装问题

```bash
# 清理 npm 缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# Python 依赖问题
pip cache purge
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

## 12. 开发工作流

### 12.1 分支管理

```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature

# 创建 Pull Request
```

### 12.2 代码规范

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 Conventional Commits 规范
- 编写单元测试覆盖新功能
- 更新相关文档

### 12.3 性能监控

```bash
# 前端性能分析
npm run analyze

# 后端性能监控
npm run profile

# 数据库查询分析
npm run db:explain
```
