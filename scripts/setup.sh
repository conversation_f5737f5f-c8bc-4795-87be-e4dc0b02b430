#!/bin/bash

# AI-Doc 项目设置脚本
# 用于快速设置开发环境

set -e

echo "🚀 开始设置 AI-Doc 开发环境..."

# 检查必需的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装。请安装 Node.js 18+ 版本"
        exit 1
    fi
    
    node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        echo "❌ Node.js 版本过低。需要 18+ 版本，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 未安装。请安装 Python 3.9+ 版本"
        exit 1
    fi
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        echo "✅ Docker 已安装"
        DOCKER_AVAILABLE=true
    else
        echo "⚠️  Docker 未安装，将跳过 Docker 相关设置"
        DOCKER_AVAILABLE=false
    fi
    
    echo "✅ 系统要求检查通过"
}

# 创建环境变量文件
setup_env() {
    echo "🔧 设置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "📝 已创建 .env 文件，请编辑并填入必要的配置"
        echo "⚠️  特别注意设置 GEMINI_API_KEY"
    else
        echo "✅ .env 文件已存在"
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装根目录依赖
    echo "安装根目录依赖..."
    npm install
    
    # 安装前端依赖
    echo "安装前端依赖..."
    cd frontend
    npm install
    cd ..
    
    # 安装后端依赖
    echo "安装后端依赖..."
    cd backend
    npm install
    cd ..
    
    # 安装 AI 服务依赖
    echo "安装 AI 服务依赖..."
    cd ai-service
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        echo "✅ 已创建 Python 虚拟环境"
    fi
    
    # 激活虚拟环境并安装依赖
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    deactivate
    cd ..
    
    echo "✅ 所有依赖安装完成"
}

# 设置数据库
setup_database() {
    echo "🗄️  设置数据库..."
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        echo "使用 Docker 启动数据库服务..."
        docker-compose up -d postgres redis
        
        # 等待数据库启动
        echo "等待数据库启动..."
        sleep 10
        
        # 运行数据库迁移
        cd backend
        npm run db:migrate
        npm run db:seed
        cd ..
        
        echo "✅ 数据库设置完成"
    else
        echo "⚠️  请手动设置 PostgreSQL 和 Redis 数据库"
        echo "   PostgreSQL: 创建数据库 'ai_doc'"
        echo "   Redis: 默认配置即可"
    fi
}

# 构建项目
build_project() {
    echo "🔨 构建项目..."
    
    # 构建后端
    echo "构建后端..."
    cd backend
    npm run build
    cd ..
    
    # 构建前端
    echo "构建前端..."
    cd frontend
    npm run build
    cd ..
    
    echo "✅ 项目构建完成"
}

# 运行测试
run_tests() {
    echo "🧪 运行测试..."
    
    # 前端测试
    echo "运行前端测试..."
    cd frontend
    npm test -- --run
    cd ..
    
    # 后端测试
    echo "运行后端测试..."
    cd backend
    npm test
    cd ..
    
    # AI 服务测试
    echo "运行 AI 服务测试..."
    cd ai-service
    source venv/bin/activate
    pytest
    deactivate
    cd ..
    
    echo "✅ 所有测试通过"
}

# 显示启动说明
show_instructions() {
    echo ""
    echo "🎉 AI-Doc 开发环境设置完成！"
    echo ""
    echo "📚 启动说明："
    echo "1. 开发模式启动所有服务："
    echo "   npm run dev"
    echo ""
    echo "2. 单独启动服务："
    echo "   前端: cd frontend && npm run dev"
    echo "   后端: cd backend && npm run dev"
    echo "   AI服务: cd ai-service && source venv/bin/activate && python -m uvicorn src.main:app --reload"
    echo ""
    echo "3. 使用 Docker："
    echo "   docker-compose up -d"
    echo ""
    echo "🌐 访问地址："
    echo "   前端应用: http://localhost:3000"
    echo "   后端 API: http://localhost:3001"
    echo "   AI 服务: http://localhost:8000"
    echo "   API 文档: http://localhost:3001/api-docs"
    echo ""
    echo "⚠️  注意事项："
    echo "1. 请确保在 .env 文件中设置了正确的 GEMINI_API_KEY"
    echo "2. 首次启动可能需要等待数据库初始化"
    echo "3. 如遇到问题，请查看各服务的日志输出"
    echo ""
}

# 主函数
main() {
    check_requirements
    setup_env
    install_dependencies
    
    # 询问是否设置数据库
    read -p "是否设置数据库? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
    fi
    
    # 询问是否构建项目
    read -p "是否构建项目? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_project
    fi
    
    # 询问是否运行测试
    read -p "是否运行测试? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    show_instructions
}

# 运行主函数
main "$@"
