# 依赖文件
node_modules/
*/node_modules/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
dist/
build/
*/dist/
*/build/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output/
*/coverage/

# 缓存目录
.cache/
.parcel-cache/
.next/
.nuxt/

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/
.tox/
.mypy_cache/
.dmypy.json
dmypy.json

# 数据库
*.db
*.sqlite
*.sqlite3

# 上传文件
uploads/
temp/
tmp/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Docker
.dockerignore

# 测试
test-results/
playwright-report/
test-results.xml

# 其他
.eslintcache
.stylelintcache
