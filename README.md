# 智能文档系统 (AI-Doc)

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![Python Version](https://img.shields.io/badge/python-%3E%3D3.9-blue.svg)](https://python.org/)

一个现代化的智能文档协作平台，集成了实时协作、AI 辅助写作和丰富的编辑功能。基于 React + TipTap 构建，支持多人实时协作编辑，提供强大的 AI 写作助手功能。

## ✨ 核心特性

### 📝 富文本编辑
- **现代编辑器**: 基于 TipTap/ProseMirror 的无头编辑器架构
- **Markdown 支持**: 实时 Markdown 语法渲染和转换
- **块编辑器**: 类似 Notion 的灵活块编辑模式
- **丰富格式**: 支持表格、列表、图片、链接等多种内容类型
- **快捷操作**: 完整的键盘快捷键支持

### 🤝 实时协作
- **多人编辑**: 支持多用户同时编辑同一文档
- **实时同步**: 基于 Y.js CRDT 算法，毫秒级同步
- **协作光标**: 显示其他用户的光标位置和选择范围
- **权限管理**: 细粒度的文档访问权限控制
- **评论系统**: 支持文档评论、回复和 @提醒功能

### 🤖 AI 智能助手
- **聊天式编辑**: 通过自然语言指令进行文档编辑
- **智能写作**: 场景化写作、大纲生成、内容扩展
- **智能改写**: 文本润色、风格调整、摘要生成
- **智能校对**: 实时语法检查、拼写纠错
- **多模态生成**: 支持图表、流程图等可视化内容生成

### 📁 文档管理
- **文件夹系统**: 层级化的文档组织结构
- **模板库**: 丰富的预设模板和自定义模板
- **版本历史**: 完整的文档版本追踪和恢复
- **导入导出**: 支持 .docx、.pdf、.md、.txt 等多种格式
- **全文搜索**: 基于语义的智能搜索功能

## 🏗️ 技术架构

### 前端技术栈
- **React 18**: 现代化的用户界面框架
- **TipTap**: 基于 ProseMirror 的富文本编辑器
- **Material-UI (MUI)**: 现代化的 UI 组件库
- **Redux Toolkit**: 状态管理
- **WebSocket**: 实时通信

### 后端技术栈
- **Node.js + Express**: 核心业务服务
- **Python + FastAPI**: AI 服务
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和消息队列
- **Y.js**: 实时协作引擎

### AI 和协作
- **Y.js CRDT**: 冲突自由的实时协作算法
- **Gemini API**: 大语言模型集成
- **Pandoc**: 文档格式转换

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- Python >= 3.9
- PostgreSQL >= 14
- Redis >= 6.0

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-org/ai-doc.git
cd ai-doc
```

2. **安装依赖**
```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install

# 安装 AI 服务依赖
cd ../ai-service
pip install -r requirements.txt
```

3. **环境配置**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

4. **数据库初始化**
```bash
# 创建数据库
createdb ai_doc

# 运行数据库迁移
npm run db:migrate
```

5. **启动服务**
```bash
# 启动后端服务
npm run dev:backend

# 启动 AI 服务
npm run dev:ai

# 启动前端服务
npm run dev:frontend
```

访问 http://localhost:3000 开始使用！

## 📖 详细文档

### 项目文档
- [需求说明书](./requirements.md) - 详细的功能需求和非功能需求
- [技术选型文档](./TECH_SELECTION.md) - 技术栈选择和对比分析
- [系统架构设计](./ARCHITECTURE.md) - 系统架构和组件设计
- [数据结构设计](./DATA_STRUCTURE.md) - 数据库设计和数据模型

### 开发指南
- [API 文档](./docs/API.md) - RESTful API 接口文档
- [前端组件文档](./docs/COMPONENTS.md) - React 组件设计和使用
- [部署指南](./docs/DEPLOYMENT.md) - 生产环境部署说明
- [开发环境配置](./docs/DEVELOPMENT.md) - 本地开发环境搭建

## 🔧 开发

### 项目结构
```
ai-doc/
├── frontend/                 # React 前端应用
│   ├── src/
│   │   ├── components/      # React 组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义 Hooks
│   │   ├── store/          # Redux 状态管理
│   │   └── utils/          # 工具函数
│   └── package.json
├── backend/                  # Node.js 后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   └── utils/          # 工具函数
│   └── package.json
├── ai-service/              # Python AI 服务
│   ├── src/
│   │   ├── api/           # FastAPI 路由
│   │   ├── models/        # AI 模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   └── requirements.txt
├── docs/                    # 项目文档
├── scripts/                 # 构建和部署脚本
└── docker/                  # Docker 配置文件
```

### 开发命令

```bash
# 开发环境
npm run dev              # 启动所有服务
npm run dev:frontend     # 仅启动前端
npm run dev:backend      # 仅启动后端
npm run dev:ai           # 仅启动 AI 服务

# 测试
npm run test             # 运行所有测试
npm run test:frontend    # 前端测试
npm run test:backend     # 后端测试
npm run test:ai          # AI 服务测试

# 构建
npm run build            # 构建生产版本
npm run build:frontend   # 构建前端
npm run build:backend    # 构建后端

# 数据库
npm run db:migrate       # 运行数据库迁移
npm run db:seed          # 填充测试数据
npm run db:reset         # 重置数据库

# 代码质量
npm run lint             # 代码检查
npm run format           # 代码格式化
npm run type-check       # TypeScript 类型检查
```

## 🐳 Docker 部署

### 使用 Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 环境变量配置

```bash
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/ai_doc
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=7d

# AI 服务配置
GEMINI_API_KEY=your-gemini-api-key
AI_SERVICE_URL=http://localhost:8000

# 对象存储配置
S3_BUCKET=ai-doc-storage
S3_ACCESS_KEY=your-access-key
S3_SECRET_KEY=your-secret-key
S3_REGION=us-east-1

# 应用配置
NODE_ENV=production
PORT=3000
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行特定测试套件
npm test -- --testNamePattern="Document"

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行端到端测试
npm run test:e2e
```

### 测试结构

```
tests/
├── unit/                    # 单元测试
│   ├── frontend/           # 前端单元测试
│   ├── backend/            # 后端单元测试
│   └── ai-service/         # AI 服务单元测试
├── integration/            # 集成测试
├── e2e/                    # 端到端测试
└── fixtures/               # 测试数据
```

## 🚀 生产部署

### 系统要求

- **服务器**: 4 核 CPU, 8GB RAM, 100GB SSD
- **数据库**: PostgreSQL 14+ (推荐使用托管服务)
- **缓存**: Redis 6+ (推荐使用托管服务)
- **对象存储**: AWS S3 或兼容服务
- **域名**: 支持 HTTPS 的域名

### 部署步骤

1. **服务器准备**
```bash
# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 Python
sudo apt-get install -y python3 python3-pip
```

2. **代码部署**
```bash
# 克隆代码
git clone https://github.com/your-org/ai-doc.git
cd ai-doc

# 配置环境变量
cp .env.production .env
vim .env

# 构建和启动
docker-compose -f docker-compose.prod.yml up -d
```

3. **反向代理配置 (Nginx)**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /ws/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Host $host;
    }
}
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读我们的贡献指南了解详情。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 编写单元测试覆盖新功能
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [TipTap](https://tiptap.dev/) - 强大的富文本编辑器框架
- [ProseMirror](https://prosemirror.net/) - 富文本编辑器核心
- [Y.js](https://github.com/yjs/yjs) - 实时协作算法实现
- [Material-UI](https://mui.com/) - React UI 组件库

## 📞 联系我们

- 项目主页: https://github.com/your-org/ai-doc
- 问题反馈: https://github.com/your-org/ai-doc/issues
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给我们一个 Star！