version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-doc-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ai_doc
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ai-doc-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ai_doc"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ai-doc-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-doc-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-doc-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/ai_doc
      REDIS_URL: redis://:redis_password@redis:6379
      JWT_SECRET: your-super-secret-jwt-key
      PORT: 3001
      FRONTEND_URL: http://localhost:3000
      AI_SERVICE_URL: http://ai-service:8000
    ports:
      - "3001:3001"
      - "3002:3002"  # WebSocket 端口
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads_data:/app/uploads
    networks:
      - ai-doc-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI 服务
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    container_name: ai-doc-ai-service
    restart: unless-stopped
    environment:
      ENVIRONMENT: development
      HOST: 0.0.0.0
      PORT: 8000
      REDIS_URL: redis://:redis_password@redis:6379
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      SECRET_KEY: your-super-secret-key
      LOG_LEVEL: INFO
    ports:
      - "8000:8000"
    volumes:
      - ./ai-service:/app
      - ai_models_cache:/app/models
    networks:
      - ai-doc-network
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-doc-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:3001/api
      VITE_WS_URL: ws://localhost:3002
      VITE_AI_URL: http://localhost:8000/api/v1
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ai-doc-network
    depends_on:
      - backend
      - ai-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: ai-doc-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - ai-doc-network
    depends_on:
      - frontend
      - backend
      - ai-service
    profiles:
      - production

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-doc-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - ai-doc-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: ai-doc-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-doc-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

# 网络配置
networks:
  ai-doc-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  ai_models_cache:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
