# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/ai_doc
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# AI 服务配置
GEMINI_API_KEY=your-gemini-api-key-here
AI_SERVICE_URL=http://localhost:8000

# 对象存储配置 (AWS S3 或兼容服务)
S3_BUCKET=ai-doc-storage
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_REGION=us-east-1
S3_ENDPOINT=https://s3.amazonaws.com

# 应用配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
WS_PORT=3002

# CORS 配置
CORS_ORIGIN=http://localhost:3000

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# 邮件服务配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX_REQUESTS=100

# 协作配置
COLLABORATION_TIMEOUT=30000  # 30秒
MAX_COLLABORATORS=50

# 缓存配置
CACHE_TTL=3600  # 1小时

# 监控配置 (可选)
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_ID=your-analytics-id-here
